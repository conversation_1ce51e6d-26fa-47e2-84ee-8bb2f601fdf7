<?php

namespace App\Http\Controllers\GeneralUser;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\UserVerificationEmail;
use App\Models\User;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Mail;
use App\Mail\TokenVerificationMail;
use Illuminate\Support\Facades\Auth;
use App\Models\Applicant;
use App\Models\Employment;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Storage;
use App\Models\ParticipantProfile;
use App\Mail\TokenVerificationUpdateEmailMail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use App\Models\ApplyEmployment;
use Illuminate\Support\Str;
use App\Mail\VerificationApplyEmploymentEmailMail;
use App\Mail\ApplyEmploymentSuccessMail;

class GeneralUser extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //empleos aplicados
        $appliedEmployments = Applicant::where('user_id', Auth::user()->id)->get();
        return view('web.pages.auth.profile', compact('appliedEmployments'));
    }

    public function sendTokenVerificationEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
        ],[
            'email.required' => 'Por favor, ingresa tu correo electrónico.',
            'email.email'    => 'Por favor, ingresa un correo electrónico válido.',
            'email.unique'   => 'El correo electrónico ya está registrado.',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        //veficar que el email no existe en la base de usuarios
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            Alert::toast('El email ya está registrado.', 'danger');
            return redirect()->route('iniciar-sesion');
        }
        do {
            $token = (string) random_int(10000000, 99999999);
        } while (UserVerificationEmail::where('token', $token)->exists());

        //guardar token en la base
        UserVerificationEmail::create([
            'email' => $request->email,
            'token' => $token,
            'expires_at' => now()->addMinutes(15),
        ]);
        //enviar email con el token
        Mail::to($request->email)->send(new TokenVerificationMail($token));
        Alert::toast('Recibirás un código de verificación para crear tu cuenta que deberás ingresar en el formulario de registro', 'success');
        return redirect()->route('formulario-registro');
    }

    //actualizar perfil
    public function updateProfile(Request $request)
    {
        $request->validate([
            'phonenumber' => ['required', 'digits:8'],
            'dui' => ['required', 'digits:9', 'unique:participant_profiles,dui,' . Auth::user()->participantProfile->id],
            'cv' => ['nullable', 'file', 'mimes:pdf'],
        ], [
            'phonenumber.required' => 'Por favor, ingresa tu número de celular.',
            'dui.required' => 'Por favor, ingresa tu número de DUI.',
            'cv.mimes' => 'El currículum vitae debe ser un archivo PDF.',
            'dui.unique' => 'El DUI ya está registrado.',
        ]);
        //verificar que phonenumber no exista
        $existingPhoneNumber = ParticipantProfile::where('phonenumber', $request->phonenumber)->where('id', '!=', Auth::user()->participantProfile->id)->first();
        if ($existingPhoneNumber) {
            Alert::toast('El número de celular ya está registrado.', 'warning');
            return redirect()->back()->withInput();
        }
        try {
            $applicant = ParticipantProfile::find(Auth::user()->participantProfile->id);
            $dataToUpdate = [
                'phonenumber' => $request->phonenumber,
                'dui' => $request->dui,
            ];
            if ($request->hasFile('cv')) {
                if ($applicant->cv_file) {
                    Storage::disk('private')->delete($applicant->cv_file);
                }
                $file = $request->file('cv');
                $extension = $file->getClientOriginalExtension();
                $user = Auth::user();
                $filename = $request->dui . '_' . $user->name . '_' . $user->last_name . '.' . $extension;
                $cvPath = $file->storeAs('CVs', $filename, 'private');
                $dataToUpdate['cv_file'] = $cvPath;
            }
            //Actualizar el perfil del participante con los datos
            $applicant->update($dataToUpdate);
            Alert::toast('Perfil actualizado exitosamente.', 'success');
            return redirect()->route('profile-general-user');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar tu perfil. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //DESCARGAR CV
    public function downloadCv($user_id, $cv_file)
    {
        if (Auth::id() != $user_id) {
            Alert::error('Error', 'No tienes permiso para descargar este archivo.');
            return redirect()->back();
        }
        $filePath = $cv_file;
        if (Storage::disk('private')->exists($filePath)) {
            $absolutePath = Storage::disk('private')->path($filePath);
            return response()->download($absolutePath);
        }
        Alert::error('Error', 'El archivo no se encuentra.');
        return redirect()->back();
    }

    //descargar
    public function downloadCvpdf($id = null)
    {
        $user = Auth::user();
        $profile = ParticipantProfile::where('user_id', $user->id)->firstOrFail();

        if (!$profile->cv_file || !Storage::disk('private')->exists($profile->cv_file)) {
            Alert::toast('No se encontró tu CV en el sistema.', 'error');
            return redirect()->back();
        }
        // return Storage::disk('private')->download($profile->cv_file);
        $absolutePath = Storage::disk('private')->path($profile->cv_file);
        return response()->download($absolutePath);
    }

    // aplicar a un empleo
    public function applyEmployment(Request $request)
    {
        $user = Auth::user();
        $dbUser = User::find($user->id);

        if (!$user) {
            Alert::toast('No está autenticado.', 'error');
            return redirect()->back();
        }

        if (!$dbUser->hasRole('Participante')) {
            Alert::toast('No tienes permisos para acceder a esta sección.', 'error');
            return redirect()->back()->withInput();
        }

        // Si el perfil del participante está incompleto, no puede aplicar.
        if ($user->participantProfile?->phonenumber == null || $user->participantProfile?->cv_file == null || $user->participantProfile?->dui == null) {
            Alert::toast('Debe completar su perfil para aplicar a este empleo.', 'error');
            return redirect()->back()->withInput();
        }

        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:employments,id',
            'slug' => 'required|exists:employments,slug',
        ], [
            'id.required' => 'El empleo es obligatorio.',
            'id.exists' => 'El empleo no existe.',
            'slug.required' => 'URL invalida.',
        ]);

        if ($validator->fails()) {
            Alert::toast('El empleo no existe.', 'error');
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $job = Employment::where('id', $request->id)->where('slug', $request->slug)->first();
        if (!$job) {
            Alert::toast('El empleo no existe.', 'error');
            return redirect()->back()->withInput();
        }

        try {
            // Verificar si ya aplicó antes a esta vacante
            $existingApplication = Applicant::where('user_id', $user->id)
                ->where('employment_id', $job->id)
                ->latest()
                ->first();

            if ($existingApplication) {
                $daysSinceLastApplication = now()->diffInDays($existingApplication->created_at);
                // Caso 1: Si fue rechazado (status_id = 7)
                if ($existingApplication->status_id == 7) {
                    if ($daysSinceLastApplication < 15) {
                        Alert::toast('Ya fuiste rechazado en esta vacante recientemente. Debes esperar 15 días para volver a aplicar.', 'error');
                        return redirect()->back();
                    }
                }
                // Caso 2: Si ya aplicó y sigue activo (cualquier otro status)
                else {
                    if ($daysSinceLastApplication < 15) {
                        Alert::toast('Ya aplicaste a este empleo recientemente. Debes esperar al menos 15 días para volver a aplicar.', 'error');
                        return redirect()->back();
                    }
                }
            }
            // Crear nueva postulación
            Applicant::create([
                'user_id' => $user->id,
                'participant_profile_id' => $user->participantProfile->id,
                'employment_id' => $job->id,
                'status_id' => 7, // Postulación nueva
            ]);
            Alert::toast('Has aplicado exitosamente a este empleo.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al aplicar a este empleo. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar correo de usuario general
    public function requestUpdateEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        $user = Auth::user();
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            Alert::toast('El correo electrónico no está disponible.', 'danger');
            return redirect()->route('profile-general-user')->withErrors(['email' => 'El correo electrónico no está disponible.']);
        }
        //recolectando informacion para formar la url
        try {
            $datos = [
                'user' => $user->id,
                'new_email' => $request->email,
                'old_email' => $user->email,
            ];
            //generar una url temporal firmada que expira 15 minutos
            $url = URL::temporarySignedRoute(
                'email.verify.update',
                now()->addMinutes(15),
                $datos
            );
            //enviar correo con la url
            Mail::to($request->email)->send(new TokenVerificationUpdateEmailMail($url));
            Alert::toast('Recibirás un correo de verificación para confirmar el cambio de correo.', 'success');
            return redirect()->route('profile-general-user');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al enviar el correo de verificación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //verificar datos para actualizar correo de usuario general
    public function verifyDataUpdateEmail(Request $request)
    {
        if (!URL::hasValidSignature($request)) {
            Alert::toast('El enlace de verificación no es válido.', 'danger');
            return redirect()->route('profile-general-user');
        }
        $user = User::findOrFail($request->user);

        if (!$user) {
            return redirect()->route('profile-general-user')->with('error', 'El enlace de verificación no es válido.');
        }
        //si el usuario del enlace no está logueado, redirigir para inicar sesion.
        if ($user->id !== Auth::id()) {
            Auth::logout();
            return redirect()->route('acceder')->with('status', 'Por favor, inicia sesión para completar la verificación.');
        }
        try {
            $user->update([
                'email' => $request->new_email,
                'email_verified_at' => now(),
            ]);
            Alert::toast('Correo actualizado exitosamente.', 'success');
            return redirect()->route('profile-general-user');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al actualizar el correo. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
    //si el admin creo el usuario participante, que confirme su email
    public function verifyEmailUser(Request $request)
    {
        if (!URL::hasValidSignature($request)) {
            Alert::toast('El enlace de verificación no es válido.', 'danger');
            return redirect()->route('profile-general-user');
        }
        $user = User::findOrFail($request->user);

        if (!$user) {
            return redirect()->route('profile-general-user')->with('error', 'El enlace de verificación no es válido.');
        }
        try {
            $user->update([
                'email_verified_at' => now(),
            ]);
            Alert::toast('Email verificado exitosamente.', 'success');
            return redirect()->route('profile-general-user');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al verificar el email. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    //actualizar contrasena del usuario logueado
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);
        $user = Auth::user();
        if (!Hash::check($request->current_password, $user->password)) {
            Alert::toast('La contraseña actual no coincide.', 'error');
            return redirect()->back()->withInput();
        }
        User::where('id', $user->id)->update([
            'password' => Hash::make($request->password),
        ]);
        Alert::toast('Contraseña actualizada exitosamente.', 'success');
        return redirect()->route('profile-general-user');
    }

    //aplicar a un empleo (metodo 2)
    public function sendInfoToApplyEmployment(Request $request)
    {
        //validar
        $request->validate([
            'full_name' => 'required|string|max:255|min:10',
            'email' => 'required|string|email|max:255',
            'cv' => 'required|file|mimes:pdf|max:5000',
            'employment_id' => 'required|exists:employments,id',
            'slug' => 'required|exists:employments,slug',
        ], [
            'full_name.required' => 'Por favor, ingresa tu nombre completo.',
            'full_name.string' => 'El nombre completo debe ser texto.',
            'full_name.max' => 'El nombre completo no debe exceder los :max caracteres.',
            'full_name.min' => 'El nombre completo debe tener al menos :min caracteres.',
            'email.required' => 'Por favor, ingresa tu correo electrónico.',
            'email.string' => 'El correo electrónico debe ser texto.',
            'email.email' => 'El formato del correo electrónico no es válido.',
            'email.max' => 'El correo electrónico no debe exceder los :max caracteres.',
            'cv.required' => 'Por favor, sube tu currículum vitae.',
            'cv.file' => 'El currículum vitae debe ser un archivo.',
            'cv.mimes' => 'El currículum vitae debe ser un archivo PDF.',
            'cv.max' => 'El currículum vitae no debe exceder los :max KB.',
        ]);

        // $cacheKey = 'pending_application:' . $request->employment_id . ':' . $request->email;
        // if (Cache::has($cacheKey)) {
        //     Alert::toast('Ya has enviado una solicitud de aplicación para este empleo. Por favor, espera a que se confirme tu aplicación.', 'warning');
        //     return redirect()->back()->withInput();
        // }

        //verificar que el empleado existe
        $employment = Employment::where('id', $request->employment_id)->first();
        if (!$employment) {
            // empleo no existe
            Alert::toast('Ocurrio un error grave al aplicar. Ingrese unicamente los datos que se le solicitan.', 'error');
            return redirect()->back()->withInput();
        }

        //verificar que la url (slug) es correcta con el id del empleo
        if ($employment->slug != $request->slug) {
            // url no es correcta
            Alert::toast('Ocurrio un error grave al aplicar. Ingrese unicamente los datos que se le solicitan.', 'error');
            return redirect()->back()->withInput();
        }

        //obtener info del empleo
        $employmentTitle = $employment->title;
        $name = $request->full_name;

        try {
            //subir cv a storage
            $file = $request->file('cv');
            $extension = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '.' . $extension;
            $cvPath = $file->storeAs('TempCVs', $filename, 'private');
            //enviar correo de verificacion
            $data = [
                'employment_id' => $employment->id,
                'email' => $request->email,
                'full_name' => Str::upper($request->full_name),
                'cv_name' => $filename,
                'cv_temp_path' => str_replace('/', '|', $cvPath),
            ];
            $url = URL::temporarySignedRoute(
                'email.confirm.apply.employment',
                now()->addMinutes(60),
                $data
            );
            //enviar correo con la url
            Mail::to($request->email)->send(new VerificationApplyEmploymentEmailMail($url, $name, $employmentTitle));
            //agregar a cache para evitar spam
            // Cache::put($cacheKey, true, now()->addMinutes(60));
            Alert::toast('Aplicación enviada exitosamente.', 'success');
            return redirect()->back();
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al enviar la aplicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    public function verifyDataApplyEmployment(Request $request)
    {
        if (!URL::hasValidSignature($request)) {
            Alert::toast('El enlace de verificación no es válido.', 'danger');
            return redirect()->route('home');
        }
        try {
            $data = [
                'employment_id' => $request->get('employment_id'),
                'email' => $request->get('email'),
                'full_name' => $request->get('full_name'),
                'cv_name' => $request->get('cv_name'),
                'cv_temp_path' => str_replace('|', '/', $request->get('cv_temp_path')),
            ];
            //nombre del empleo
            $employment = Employment::where('id', $data['employment_id'])->first();
            $employmentTitle = $employment->title;
            //verificar que el email no exista aplicando al mismo empleo
            $existingApplication = ApplyEmployment::where('email', $data['email'])
                ->where('employment_id', $data['employment_id'])
                ->first();
            if ($existingApplication) {
                Alert::toast('Este correo electrónico ya ha aplicado a este empleo.', 'warning');
                return redirect()->route('home'); 
            }
            // Verificar que el archivo temporal existe antes de moverlo
            if (!Storage::exists($data['cv_temp_path'])) {
                Alert::toast('El archivo temporal no existe.', 'error');
                return redirect()->back()->withInput();
            }
            // Mover el archivo dentro del disco 'private'.
            $cvPath = Storage::disk('private')->move($data['cv_temp_path'], 'CVs/' . $data['cv_name']);
            //borrar cv de storage temporal
            Storage::delete($data['cv_temp_path']);

            //crear postulacion
            ApplyEmployment::create([
                'full_name' => $data['full_name'],
                'email' => $data['email'],
                'cv' => $cvPath,
                'employment_id' => $data['employment_id'],
                'status_id' => 7, // Postulación nueva
            ]);
            //limpiar cache
            // Cache::forget('pending_application:' . $data['employment_id'] . ':' . $data['email']);
            //enviar correo de confirmacion
            Mail::to($data['email'])->send(new ApplyEmploymentSuccessMail($data['full_name'], $employmentTitle));
            Alert::toast('Aplicación confirmada exitosamente.', 'success');
            return redirect()->route('home');
        } catch (\Throwable $th) {
            Alert::toast('Ocurrió un error al confirmar la aplicación. Error: ' . $th->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }
}
