<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ApplyEmployment extends Model
{
    protected $fillable = [
        'full_name',
        'email',
        'cv',
        'employment_id',
        'status_id',
    ];
    public function employment()
    {
        return $this->belongsTo(Employment::class);
    }
    public function status()
    {
        return $this->belongsTo(Status::class);
    }
}
