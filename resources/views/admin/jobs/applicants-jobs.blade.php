@extends('adminlte::page')

@section('title', "Aplicantes a " . $job->title)

@section('content_header')
    <h1>{{ $job->title }}</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12 col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Listado de postulantes a: <td>{{ $job->title }}</td></h3>
            </div>
            <div class="card-body table-responsive">
                <table class="table table-head-fixed text-nowrap table-responsive-lg table-hover" id="tablaPostulantes">
                    <thead>
                        <tr>
                            <th>Aplicantes</th>
                            <th>Correo electrónico</th>
                            <th>Fecha de aplicación</th>
                            <th>Estado</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($applicants as $applicant)
                            <tr>
                                <td>{{ $applicant->full_name }}</td>
                                <td>{{ $applicant->email }}</td>
                                <td>{{ $applicant->created_at->toDateString() }} || {{ $applicant->created_at->diffForHumans() }}</td>
                                <td>
                                    @if ($applicant->status_id == 7)
                                        <span class="badge badge-warning">{{ $applicant->status->name }}</span>
                                    @elseif ($applicant->status_id == 8)
                                        <span class="badge badge-primary">{{ $applicant->status->name }}</span>
                                    @elseif ($applicant->status_id == 9)
                                        <span class="badge badge-success">{{ $applicant->status->name }}</span>
                                    @elseif ($applicant->status_id == 10)
                                        <span class="badge badge-danger">{{ $applicant->status->name }}</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.job.view.applicant', $applicant->id) }}" class="btn btn-secondary">Detalles</a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    table th, table td {
        vertical-align: middle !important;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        $('#tablaPostulantes').DataTable( {
            language: {
                "search": "Buscar",
                "zeroRecords": "No se encontraron elementos coincidentes",
                "lengthMenu": "Mostrar _MENU_ elementos",
                "loadingRecords": "Cargando...",
                "processing": "Procesando...",
                "info": "Mostrando del _START_ al _END_ de _TOTAL_ elementos",
                "infoEmpty": "Mostrando del 0 al 0 de 0 elementos",
                "infoFiltered": "(filtrados de _MAX_ elementos totales)",
                "paginate": {
                    "previous": '<i class="fas fa-chevron-left"></i>',
                    "next": '<i class="fas fa-chevron-right"></i>',
                    "first": "Inicio",
                    "last": "Fin"
                }
            },
            "lengthMenu": [[20,30,50, -1], [20,30,50, "Todo"]],
            dom: 'lfrtip',
            buttons: [
                'copy',
                'csv',
                'excel',
                'pdf',
            ]
        });
    });
</script>
@stop