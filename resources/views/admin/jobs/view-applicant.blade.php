@extends('adminlte::page')

@section('title', 'Detalles de postulante')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">
                    <i class="fas fa-briefcase"></i> {{ $applicant->user->name }} {{ $applicant->user->last_name }} 
                    <span class="badge badge-info">{{ $applicant->status->name }}</span>
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    {{-- <li class="breadcrumb-item"><a href="{{ route('admin.job.view.applicants') }}">Postulantes</a></li> --}}
                    <li class="breadcrumb-item"><a href="{{ route('admin.job.view.applicants', ['id' => $applicant->employment->id]) }}">Postulantes</a></li>
                    <li class="breadcrumb-item active">{{ $applicant->user->name}}</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        {{-- COLUMNA PRINCIPAL DE CONTENIDO --}}
        <div class="col-md-9">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit"></i> Información de la postulante
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {{-- Nombre de la postulante --}}
                    <div class="row">
                        <div class="col-12 col-sm-4 col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-success elevation-1">
                                    <i class="fas fa-user"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{{ $applicant->user->name }} {{ $applicant->user->last_name }}</span>
                                    <span class="info-box-number">Postulante</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-4 col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary elevation-1">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{{ $applicant->user->email }}</span>
                                    <span class="info-box-number">Email</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-4 col-md-4">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary elevation-1">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{{ $applicant->participantProfile->phonenumber }}</span>
                                    <span class="info-box-number">Teléfono</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- CV FILE iframe--}}
                    {{-- <div class="form-group">
                        <iframe src="{{ asset('storage/' . $cvPath) }}" width="100%" height="500px"></iframe>
                    </div> --}}
                    {{-- <div class="card card-outline card-info"> --}}
                        {{-- <div class="card-header">
                            <h3 class="card-title">Documento: CV</h3>
                        </div> --}}
                        {{-- <div class="card-body p-0"> --}}
                            {{-- Usar la nueva ruta para el iframe --}}
                            <hr>
                            <iframe src="{{ asset('storage/' . $cvPath) }}" style="width: 100%; height: 800px; border: none;"></iframe>
                        {{-- </div> --}}
                        <div class="card-footer text-center">
                            <a href="{{ route('admin.job.applicant.cv', $applicant->user->id) }}" class="btn btn-primary" download>
                                <i class="fas fa-download"></i> Descargar CV
                            </a>
                        </div>
                    {{-- </div> --}}
                </div>
            </div>
        </div>

        {{-- COLUMNA LATERAL (WIDGETS Y ACCIONES) --}}
        <div class="col-md-3">
            {{-- WIDGET DE IMAGEN ACTUAL --}}
            <div class="card card-outline card-secondary">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-user"></i> Detalle del postulante</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body text-center">
                    {{ $applicant->user->name }} {{ $applicant->user->last_name }} <br>
                    {{ $applicant->participantProfile->phonenumber }} <br>
                    {{ $applicant->user->email }}
                </div>
            </div>

            {{-- WIDGET PARA SUBIR NUEVA IMAGEN --}}
            <div class="card card-outline card-info">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-briefcase"></i> Información del puesto</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <span class="badge badge-primary" style="font-size: 1.2rem;">{{ $applicant->employment->title }}</span>
                    <hr>
                    <p>Detalles:</p>
                    {!! $applicant->employment->description!!}
                </div>
            </div>
            
            {{-- WIDGET DE INFORMACIÓN Y ACCIONES RÁPIDAS --}}
            <div class="card card-outline card-danger">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-info"></i> Estado de la postulación</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form class="form-group" action="{{ route('admin.job.update.status.applicant', $applicant->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <label for="status_id">Estado: <span>*</span></label>
                        <select class="form-control @error('status_id') is-invalid @enderror" 
                                id="status_id" name="status_id" required>
                            @foreach($statuses as $status)
                                <option value="{{ $status->id }}" 
                                    {{ (old('status_id') ?? $applicant->status_id) == $status->id ? 'selected' : '' }}>
                                    {{ $status->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('status_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <button class="btn btn-success btn-block mt-2" type="submit">
                            <i class="fas fa-save"></i> Guardar
                        </button>
                    </form>
                </div>
            </div>

            {{-- WIDGET DE FECHAS --}}
            <div class="card card-outline card-light">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-info-circle"></i> Información General</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <p><strong>Creado:</strong> {{ $applicant->created_at->diffForHumans() }}</p>
                    <p><strong>Última actualización:</strong> {{ $applicant->updated_at->diffForHumans() }}</p>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
    {{-- <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script> --}}
    {{-- <script>
        tinymce.init({
            selector: 'textarea',
            plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
            toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
            language: 'es',
            height: 400
        });

        // Script para mostrar el nombre del archivo seleccionado
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.custom-file-input').addEventListener('change', function(e) {
                var fileName = e.target.files[0].name;
                var nextSibling = e.target.nextElementSibling;
                nextSibling.innerText = fileName;
            });
        });
    </script> --}}
@stop