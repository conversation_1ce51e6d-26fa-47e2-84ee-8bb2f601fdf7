<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PagesAdminController;
use App\Http\Controllers\NewsletterSubscriberController;
use App\Http\Controllers\ContactMessageController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\JobsController;
use App\Http\Controllers\GeneralUser\AuthGeneralUser;
use App\Http\Controllers\ProfileGeneralUser;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\GeneralUser\GeneralUser;
use App\Http\Controllers\Pages\PagesController;
use App\Http\Controllers\Admin\AdminHomeController;
use App\Http\Controllers\Admin\AdminConsultingController;
use App\Http\Controllers\Admin\AdminProfessionalController;
use App\Http\Controllers\Admin\AdminAboutController;
use App\Http\Controllers\Admin\AdminEmploymentController;
use App\Http\Controllers\Admin\AdminMasterController;
use App\Http\Controllers\Admin\AdminBlogController;
use App\Http\Controllers\Admin\AdminPagesController;
use App\Http\Controllers\Admin\AdminImgPage;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\AdminSubscriberController;

//aplicar a un empleo - ESTAS RUTAS DEBEN IR ANTES QUE LAS RUTAS CON PARAMETROS
Route::post('/empleos/aplicar', [GeneralUser::class, 'sendInfoToApplyEmployment'])->name('apply-employment');
Route::get('/empleos/confirmar-aplicacion', [GeneralUser::class, 'verifyDataApplyEmployment'])->name('email.confirm.apply.employment')->middleware('signed');

//RUTAS DE LA PAGINA
Route::get('/', [PagesController::class, 'index'])->name('home');
Route::get('/nosotros', [PagesController::class, 'nosotros'])->name('nosotros');
Route::get('/consultorias', [PagesController::class, 'ConsultingSection'])->name('consultorias');
Route::get('/formacion-profesional', [PagesController::class, 'ProfessionalSection'])->name('formacion-profesional');
Route::get('/blog', [PagesController::class, 'showAllPosts'])->name('blog');
Route::get('/blog/{slug}', [PagesController::class, 'showDetailBlog'])->name('blog.detail');
Route::get('/empleos', [PagesController::class, 'showAllEmployments'])->name('empleos');
Route::get('/empleos/{slug}', [PagesController::class, 'showDetailEmployments'])->name('job.detail');
Route::get('/contactanos', [PagesController::class, 'ContactSection'])->name('contactanos');
//FORMULARIO DE SUSCRIPCION
Route::post('/newSubscriber', [PagesController::class, 'newSubscriber'])->name('newsletter.subscriber');
//ENVIO DE MENSAJES
Route::post('/newContactMessage', [PagesController::class, 'newContactMessage'])->name('contact.message');
//REGISTRO DE USUARIOS DE TIPO PARTICIPANTES
Route::get('/registro', [PagesController::class, 'registro'])->name('registro');
Route::get('/registro/formulario', [PagesController::class, 'registerForm'])->name('formulario-registro');
Route::get('/acceder', [PagesController::class, 'loginview'])->name('acceder');
Route::post('/acceder', [AuthGeneralUser::class, 'login'])->name('login-general-user');
Route::post('/registro-validar-email', [AuthGeneralUser::class, 'sendTokenVerificationEmail'])->name('validar-email');
Route::post('/registro/registro-usuario', [AuthGeneralUser::class, 'registerUserGeneral'])->name('register-user-general');
Route::get('/olvide-mi-contrasena', [AuthGeneralUser::class, 'forgotPassword'])->name('forgot-password-general-user');
Route::post('/olvide-mi-contrasena/enviar-correo', [AuthGeneralUser::class, 'sendTokenResetPassword'])->name('send.email.new.password.temporal');
Route::get('/recuperar-contrasena', [AuthGeneralUser::class, 'resetPassword'])->name('reset-password-general-user');
Route::post('/recuperar-contrasena/validar-token', [AuthGeneralUser::class, 'verifyTokenResetPassword'])->name('verify.token.reset.password');
//renviar token
Route::post('/registro/reenviar-token', [AuthGeneralUser::class, 'resendTokenVerificationEmail'])->name('resend.token.verification.email');

//para usuarios de tipo Participantes
// Route::middleware('auth', 'role:Participante')->prefix('participante')->group(function () {
//     Route::get('/perfil', [GeneralUser::class, 'index'])->name('profile-general-user');
//     Route::post('/perfil/actualizar', [GeneralUser::class, 'updateProfile'])->name('update-profile-general-user');
//     Route::post('/perfil/actualizar-contrasena', [GeneralUser::class, 'updatePassword'])->name('profile.update.password');
//     Route::post('/empleos/aplicar', [GeneralUser::class, 'applyEmployment'])->name('apply-employment');
//     Route::get('/profile/download-cv', [GeneralUser::class, 'downloadCvpdf'])->name('profile.download.cv');
//     Route::post('/logout-general-user', [AuthGeneralUser::class, 'logout'])->name('logout-general-user');
//     Route::post('/perfil/solicitar-cambio-correo', [GeneralUser::class, 'requestUpdateEmail'])->name('profile.request.update.email');
//     Route::get('/perfil/verificar-cambio-correo', [GeneralUser::class, 'verifyDataUpdateEmail'])->name('email.verify.update')->middleware('signed');

// });

//solo super admin y admins
Route::middleware('auth', 'role:Super Admin|admin')->prefix('admin')->group(function () {
    Route::get('/inicio', [AdminPagesController::class, 'index'])->name('admin.home');
    Route::post('/actualizar-informacion-empresa', [AdminPagesController::class, 'updateCompanyInformation'])->name('admin.update.company.information');
    Route::get('/users', [AdminPagesController::class, 'UserPage'])->name('admin.users.page');

    //INICIO
    Route::get('/paginas/inicio', [AdminHomeController::class, 'HomePage'])->name('admin.pages.home');
    Route::get('/paginas/inicio/banner/{id}', [AdminHomeController::class, 'viewBanner'])->name('admin.pages.view.banner');
    Route::post('/paginas/inicio/banner', [AdminHomeController::class, 'storeBanner'])->name('admin.pages.store.banner');
    Route::put('/paginas/inicio/banner/{id}', [AdminHomeController::class, 'updateBanner'])->name('admin.pages.update.banner');
    Route::delete('/paginas/inicio/banner/{id}', [AdminHomeController::class, 'deleteBanner'])->name('admin.pages.delete.banner');
    Route::post('/paginas/inicio/banner/update-order', [AdminHomeController::class, 'updateOrderBanner'])->name('admin.banners.update.order');
    Route::get('/paginas/inicio/seccion/{id}', [AdminHomeController::class, 'viewSection'])->name('admin.pages.home.view.section');
    Route::put('/paginas/inicio/seccion/{id}', [AdminHomeController::class, 'updateSection'])->name('admin.pages.update.section');
    Route::put('/paginas/inicio/subsection/{id}', [AdminHomeController::class, 'updateSubSection'])->name('admin.pages.update.subsection');

    //CONSULTORIAS
    Route::get('/paginas/consultorias', [AdminConsultingController::class, 'index'])->name('admin.pages.consulting');
    Route::get('/paginas/consultorias/crear', [AdminConsultingController::class, 'createNewDetail'])->name('admin.pages.consulting.create');
    Route::get('/paginas/consultorias/section/{id}', [AdminConsultingController::class, 'viewSection'])->name('admin.pages.consulting.view.section');
    Route::put('/paginas/consultorias/section/{id}', [AdminConsultingController::class, 'updateSection'])->name('admin.pages.consulting.update.section');
    Route::get('/paginas/consultorias/{id}', [AdminConsultingController::class, 'viewDetails'])->name('admin.pages.consulting.view.details');
    Route::put('/paginas/consultorias/{id}', [AdminConsultingController::class, 'updateDetails'])->name('admin.pages.consulting.update.details');
    Route::get('/paginas/consultorias/crear', [AdminConsultingController::class, 'createNewDetail'])->name('admin.pages.consulting.create.details');
    Route::post('/paginas/consultorias', [AdminConsultingController::class, 'storeDetails'])->name('admin.pages.consulting.store.details');
    Route::delete('/paginas/consultorias/{id}', [AdminConsultingController::class, 'deleteDetail'])->name('admin.pages.consulting.delete.detail');

    //FORMACION PROFESIONAL
    Route::get('/paginas/formacion-profesional', [AdminProfessionalController::class, 'index'])->name('admin.pages.professional');
    Route::get('/paginas/formacion-profesional/crear', [AdminProfessionalController::class, 'create'])->name('admin.pages.professional.create');
    Route::post('/paginas/formacion-profesional', [AdminProfessionalController::class, 'storeProfessional'])->name('admin.pages.professional.store');
    Route::get('/paginas/formacion-profesional/{id}', [AdminProfessionalController::class, 'viewDetails'])->name('admin.pages.professional.view.details');
    Route::put('/paginas/formacion-profesional/{id}', [AdminProfessionalController::class, 'updateProfessional'])->name('admin.pages.professional.update');
    Route::delete('/paginas/formacion-profesional/{id}', [AdminProfessionalController::class, 'deleteProfessional'])->name('admin.pages.professional.delete');

    //NOSOTROS
    Route::get('/paginas/nosotros', [AdminAboutController::class, 'index'])->name('admin.pages.about');
    Route::get('/paginas/nosotros/{id}', [AdminAboutController::class, 'viewSection'])->name('admin.pages.about.view.section');
    Route::put('/paginas/nosotros/{id}', [AdminAboutController::class, 'updateSection'])->name('admin.pages.about.update.section');
    Route::put('/paginas/nosotros/subsection/{id}', [AdminAboutController::class, 'updateSubSection'])->name('admin.pages.about.update.subsection');
    Route::get('/paginas/nosotros/valor/{id}', [AdminAboutController::class, 'viewCorporate'])->name('admin.pages.about.view.corporate');
    Route::put('/paginas/nosotros/valor/{id}', [AdminAboutController::class, 'updateCorporate'])->name('admin.pages.about.update.corporate');
    Route::post('/paginas/nosotros/imagenes/{id}', [AdminImgPage::class, 'uploadGalleryImage'])->name('admin.pages.upload.about.gallery.images');

    //MASTER AQUI HAY INFORMACION QUE RARA VEZ SE ACTUALIZARA.
    Route::get('/paginas/master', [AdminMasterController::class, 'index'])->name('admin.pages.master');
    Route::put('/paginas/master/boletin', [AdminMasterController::class, 'updateBoletin'])->name('admin.pages.master.update.boletin');
    Route::put('/paginas/master/footer', [AdminMasterController::class, 'updateFooter'])->name('admin.pages.master.update.footer');
    Route::put('/paginas/master/clientes', [AdminMasterController::class, 'updateClientes'])->name('admin.pages.master.update.clientes');
    Route::post('/paginas/master/clientes/imagenes/{id}', [AdminImgPage::class, 'uploadMultipleImagesCustomers'])->name('admin.pages.upload.customers.image');
    Route::get('/paginas/master/header/{id}', [AdminMasterController::class, 'headerView'])->name('admin.pages.header.view.details');
    Route::put('/paginas/master/header/{id}', [AdminMasterController::class, 'updateHeader'])->name('admin.pages.header.update');
    Route::get('/paginas/master/seccion/{id}', [AdminMasterController::class, 'viewDetails'])->name('admin.pages.master.view.details');

    // ELIMINAR IMAGENES DE UNA GALERIA POR SECCION Y SUBSECCION
    Route::delete('/paginas/section/imagen/{id}', [AdminImgPage::class, 'deleteSectionImage'])->name('admin.pages.delete.section.image');
    Route::delete('/paginas/subsection/imagen/{id}', [AdminImgPage::class, 'deleteSubSectionImage'])->name('admin.pages.delete.subsection.image');
    // Route::post('/paginas/section/customers/upload-image/{id}', [AdminImgPage::class, 'uploadMultipleImagesCustomers'])->name('admin.pages.upload.customers.image');

    //USUARIOS
    Route::get('/usuarios', [AdminUserController::class, 'index'])->name('admin.users');
    Route::get('/usuarios/editar/{id}', [AdminUserController::class, 'editUser'])->name('admin.user.edit');
    Route::put('/usuarios/actualizar/{id}', [AdminUserController::class, 'updateUser'])->name('admin.user.update');
    Route::get('/usuarios/enviar-correo-nueva-contraseña-temporal/{id}', [AdminUserController::class, 'sendEmailNewPasswordTemporal'])->name('admin.user.send.email.new.password');
    Route::get('/usuarios/bloquear/{id}', [AdminUserController::class, 'blockUser'])->name('admin.user.block');
    Route::post('/usuarios/registrar-admin', [AdminUserController::class, 'registerAdminUser'])->name('admin.user.register.admin');
    Route::get('/usuarios/desbloquear/{id}', [AdminUserController::class, 'unblockUser'])->name('admin.user.unblock');
    //crear usuarios
    Route::get('/usuarios/crear', [AdminUserController::class, 'createUser'])->name('admin.user.create');
    Route::post('/usuarios/crear', [AdminUserController::class, 'registerUser'])->name('admin.user.register');

    //PERFIL DEL USUARIO LOGUEADO
    Route::get('/perfil', [AdminUserController::class, 'viewEditProfile'])->name('admin.profile');
    Route::put('/perfil/actualizar', [AdminUserController::class, 'updatePassword'])->name('admin.user.update.password');

    //suscriptores
    Route::get('/suscripciones', [AdminSubscriberController::class, 'showSubscribers'])->name('admin.newsletter');
    Route::get('/suscripciones/editar/{id}', [AdminSubscriberController::class, 'editSubscriber'])->name('admin.edit.subscribers');
    Route::put('/suscripciones/actualizar/{id}', [AdminSubscriberController::class, 'updateSubscriber'])->name('admin.subscribers.update');
    Route::delete('/suscripciones/eliminar/{id}', [AdminSubscriberController::class, 'deleteSubscriber'])->name('admin.subscribers.delete');

    //mensajes
    Route::get('/mensajes', [ContactMessageController::class, 'showMessages'])->name('admin.contactmessage');
    Route::get('/mensajes/ver/{id}', [ContactMessageController::class, 'viewMessage'])->name('admin.view.message');
    Route::delete('/mensajes/eliminar/{id}', [ContactMessageController::class, 'deleteMessage'])->name('admin.delete.message');

    //blog
    Route::get('/blog', [AdminBlogController::class, 'blogView'])->name('admin.blog');
    Route::get('/blog/nuevo', [AdminBlogController::class, 'createNewPostView'])->name('admin.view.new.post');
    Route::get('/blog/{id}', [AdminBlogController::class, 'blogPostView'])->name('admin.blog.view.post');
    Route::post('/blog/nuevo-post', [AdminBlogController::class, 'newPost'])->name('posts.store');
    Route::put('/blog/actualizar-post/{id}', [AdminBlogController::class, 'update'])->name('posts.update');

    //TRABAJOS
    Route::get('/empleos', [AdminEmploymentController::class, 'index'])->name('admin.recruitment');
    Route::get('/empleos/nuevo', [AdminEmploymentController::class, 'createNewPostJob'])->name('admin.view.new.job');
    Route::get('/empleos/{id}', [AdminEmploymentController::class, 'editjob'])->name('admin.job.view');
    Route::post('/empleos/nuevo-empleo', [AdminEmploymentController::class, 'newJob'])->name('jobs.store');
    Route::put('/empleos/{id}', [AdminEmploymentController::class, 'updateJob'])->name('jobs.update');
    Route::get('/empleos/aplicantes/{id}', [AdminEmploymentController::class, 'viewApplicantsJob'])->name('admin.job.view.applicants');
    Route::get('/empleos/aplicante/{id}/cv', [AdminEmploymentController::class, 'downloadCv'])->name('admin.job.applicant.cv');
    Route::get('/empleos/aplicante/{id}', [AdminEmploymentController::class, 'viewDetailsApplicant'])->name('admin.job.view.applicant');
    Route::put('/empleos/aplicante-estado/{id}', [AdminEmploymentController::class, 'updateStatusApplicant'])->name('admin.job.update.status.applicant');
    // Route::get('/empleos/aplicante/{id}/cv', [AdminEmploymentController::class, 'getCvFile'])->name('admin.job.applicant.cv');
});

require __DIR__.'/auth.php';

// Test route for CV upload component
Route::get('/test-cv-upload', function () {
    return view('test-cv-upload');
})->name('test.cv.upload');
